<x-layouts.app title="Manage Users">
    <!-- Page Header -->
    <div class="mb-8">
        <flux:heading size="xl">Manage Users</flux:heading>
        <flux:subheading>View and manage all user accounts</flux:subheading>
    </div>

    <!-- Content -->
    <div class="space-y-6">
        @if (session('success'))
            <flux:banner variant="success" icon="check-circle">
                {{ session('success') }}
            </flux:banner>
        @endif

        @if (session('error'))
            <flux:banner variant="danger" icon="exclamation-triangle">
                {{ session('error') }}
            </flux:banner>
        @endif

        <!-- Users Table -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <flux:table>
                <flux:columns>
                    <flux:column>ID</flux:column>
                    <flux:column>Name</flux:column>
                    <flux:column>Email</flux:column>
                    <flux:column>Role</flux:column>
                    <flux:column>Status</flux:column>
                    <flux:column>Registered</flux:column>
                    <flux:column>Actions</flux:column>
                </flux:columns>

                <flux:rows>
                    @foreach ($users as $user)
                        <flux:row>
                            <flux:cell>{{ $user->id }}</flux:cell>
                            <flux:cell>{{ $user->name }}</flux:cell>
                            <flux:cell>{{ $user->email }}</flux:cell>
                            <flux:cell>
                                <flux:badge :variant="$user->role === 'admin' ? 'primary' : ($user->role === 'lister' ? 'warning' : 'secondary')">
                                    {{ ucfirst($user->role) }}
                                </flux:badge>
                            </flux:cell>
                            <flux:cell>
                                <flux:badge :variant="$user->is_active ? 'success' : 'danger'">
                                    {{ $user->is_active ? 'Active' : 'Inactive' }}
                                </flux:badge>
                            </flux:cell>
                            <flux:cell>{{ $user->created_at->format('Y-m-d') }}</flux:cell>
                            <flux:cell>
                                <form action="{{ route('admin.users.toggleStatus', $user->id) }}" method="POST" class="inline-block">
                                    @csrf
                                    @method('PATCH')
                                    <flux:button type="submit" size="sm" :variant="$user->is_active ? 'danger' : 'success'">
                                        {{ $user->is_active ? 'Deactivate' : 'Activate' }}
                                    </flux:button>
                                </form>
                            </flux:cell>
                        </flux:row>
                    @endforeach
                </flux:rows>
            </flux:table>
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            {{ $users->links() }}
        </div>
    </div>
</x-layouts.app>
