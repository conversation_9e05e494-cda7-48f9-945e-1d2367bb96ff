<x-layouts.app title="Property Management">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Property Management</h1>
        <p class="mt-2 text-gray-600">Manage all property listings and their status</p>
    </div>

    <!-- Content -->
    <div class="space-y-6">
        @if (session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        <!-- Search and Filters -->
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="mb-6">
                <flux:heading size="xl">Search & Filter</flux:heading>
            </div>

            <form action="{{ route('admin.properties.index') }}" method="GET">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <flux:field>
                        <flux:label>Keywords</flux:label>
                        <flux:input name="keywords" value="{{ request('keywords') }}" placeholder="Search properties..." />
                    </flux:field>

                    <flux:field>
                        <flux:label>Status</flux:label>
                        <flux:select name="status">
                            <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>All Statuses</option>
                            <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Published</option>
                            <option value="sold" {{ request('status') == 'sold' ? 'selected' : '' }}>Sold</option>
                            <option value="rented" {{ request('status') == 'rented' ? 'selected' : '' }}>Rented</option>
                            <option value="under_offer" {{ request('status') == 'under_offer' ? 'selected' : '' }}>Under Offer</option>
                        </flux:select>
                    </flux:field>

                    <flux:field class="flex items-end">
                        <flux:button type="submit" variant="primary">
                            <flux:icon.magnifying-glass class="size-4" />
                            Filter
                        </flux:button>
                    </flux:field>
                </div>
            </form>
        </div>

        <!-- Properties Table -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <flux:table>
                <flux:columns>
                    <flux:column>ID</flux:column>
                    <flux:column>Title</flux:column>
                    <flux:column>Lister</flux:column>
                    <flux:column>Type</flux:column>
                    <flux:column>Price</flux:column>
                    <flux:column>Status</flux:column>
                    <flux:column>Actions</flux:column>
                </flux:columns>

                <flux:rows>
                    @foreach ($properties as $property)
                        <flux:row>
                            <flux:cell>{{ $property->id }}</flux:cell>
                            <flux:cell>{{ $property->title }}</flux:cell>
                            <flux:cell>{{ $property->user->name }}</flux:cell>
                            <flux:cell>{{ $property->property_type }} ({{ $property->listing_type }})</flux:cell>
                            <flux:cell>{{ $property->currency }} {{ number_format($property->price) }}</flux:cell>
                            <flux:cell>
                                <flux:badge :variant="$property->status == 'published' ? 'success' : ($property->status == 'draft' ? 'warning' : 'secondary')">
                                    {{ ucfirst(str_replace('_', ' ', $property->status)) }}
                                </flux:badge>
                            </flux:cell>
                            <flux:cell>
                                <div class="flex items-center space-x-2">
                                    <flux:button size="sm" variant="outline" :href="route('admin.properties.edit', $property->id)">
                                        Edit
                                    </flux:button>
                                    <form action="{{ route('admin.properties.destroy', $property->id) }}" method="POST" class="inline-block">
                                        @csrf
                                        @method('DELETE')
                                        <flux:button type="submit" size="sm" variant="danger" onclick="return confirm('Are you sure you want to delete this property?')">
                                            Delete
                                        </flux:button>
                                    </form>
                                    <form action="{{ route('admin.properties.updateStatus', $property->id) }}" method="POST" class="inline-block">
                                        @csrf
                                        @method('PATCH')
                                        <flux:select name="status" size="sm" onchange="this.form.submit()">
                                            <option value="draft" {{ $property->status == 'draft' ? 'selected' : '' }}>Draft</option>
                                            <option value="published" {{ $property->status == 'published' ? 'selected' : '' }}>Publish</option>
                                            <option value="sold" {{ $property->status == 'sold' ? 'selected' : '' }}>Sold</option>
                                            <option value="rented" {{ $property->status == 'rented' ? 'selected' : '' }}>Rented</option>
                                            <option value="under_offer" {{ $property->status == 'under_offer' ? 'selected' : '' }}>Under Offer</option>
                                        </flux:select>
                                    </form>
                                </div>
                            </flux:cell>
                        </flux:row>
                    @endforeach
                </flux:rows>
            </flux:table>

            <!-- Pagination -->
            <div class="p-6 border-t border-gray-200">
                {{ $properties->links() }}
            </div>
        </div>
    </div>
</x-layouts.app>
