<x-layouts.app title="Property Management">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Property Management</h1>
        <p class="mt-2 text-gray-600">Manage all property listings and their status</p>
    </div>

    <!-- Content -->
    <div class="space-y-6">
        @if (session('success'))
            <flux:banner variant="success" icon="check-circle">
                {{ session('success') }}
            </flux:banner>
        @endif

        <!-- Search and Filters -->
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="mb-6">
                <flux:heading size="xl">Search & Filter</flux:heading>
            </div>

            <form action="{{ route('admin.properties.index') }}" method="GET">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <flux:field>
                        <flux:label>Keywords</flux:label>
                        <flux:input name="keywords" value="{{ request('keywords') }}" placeholder="Search properties..." />
                    </flux:field>

                    <flux:field>
                        <flux:label>Property Type</flux:label>
                        <flux:select name="property_type">
                            <option value="" {{ request('property_type') == '' ? 'selected' : '' }}>All Types</option>
                            <option value="apartment" {{ request('property_type') == 'apartment' ? 'selected' : '' }}>Apartment</option>
                            <option value="house" {{ request('property_type') == 'house' ? 'selected' : '' }}>House</option>
                            <option value="land" {{ request('property_type') == 'land' ? 'selected' : '' }}>Land</option>
                            <option value="single_room" {{ request('property_type') == 'single_room' ? 'selected' : '' }}>Single Room</option>
                        </flux:select>
                    </flux:field>

                    <flux:field>
                        <flux:label>Status</flux:label>
                        <flux:select name="status">
                            <option value="" {{ request('status') == '' ? 'selected' : '' }}>All Statuses</option>
                            <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Published</option>
                            <option value="sold" {{ request('status') == 'sold' ? 'selected' : '' }}>Sold</option>
                            <option value="rented" {{ request('status') == 'rented' ? 'selected' : '' }}>Rented</option>
                            <option value="under_offer" {{ request('status') == 'under_offer' ? 'selected' : '' }}>Under Offer</option>
                        </flux:select>
                    </flux:field>

                    <flux:field class="flex items-end">
                        <flux:button type="submit" variant="primary">
                            <flux:icon.magnifying-glass class="size-4" />
                            Filter
                        </flux:button>
                    </flux:field>
                </div>
            </form>
        </div>

        <!-- Properties Table -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lister</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach ($properties as $property)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->title }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->user->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->property_type }} ({{ $property->listing_type }})</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->currency }} {{ number_format($property->price) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <flux:badge :variant="$property->status == 'published' ? 'success' : ($property->status == 'draft' ? 'warning' : 'secondary')">
                                        {{ ucfirst(str_replace('_', ' ', $property->status)) }}
                                    </flux:badge>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <flux:button size="sm" variant="outline" :href="route('admin.properties.edit', $property->id)">
                                            Edit
                                        </flux:button>
                                        <form action="{{ route('admin.properties.destroy', $property->id) }}" method="POST" class="inline-block">
                                            @csrf
                                            @method('DELETE')
                                            <flux:button type="submit" size="sm" variant="danger" onclick="return confirm('Are you sure you want to delete this property?')">
                                                Delete
                                            </flux:button>
                                        </form>
                                        <form action="{{ route('admin.properties.updateStatus', $property->id) }}" method="POST" class="inline-block">
                                            @csrf
                                            @method('PATCH')
                                            <flux:select name="status" size="sm" onchange="this.form.submit()">
                                                <option value="draft" {{ $property->status == 'draft' ? 'selected' : '' }}>Draft</option>
                                                <option value="published" {{ $property->status == 'published' ? 'selected' : '' }}>Publish</option>
                                                <option value="sold" {{ $property->status == 'sold' ? 'selected' : '' }}>Sold</option>
                                                <option value="rented" {{ $property->status == 'rented' ? 'selected' : '' }}>Rented</option>
                                                <option value="under_offer" {{ $property->status == 'under_offer' ? 'selected' : '' }}>Under Offer</option>
                                            </flux:select>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="p-6 border-t border-gray-200">
                {{ $properties->links() }}
            </div>
        </div>
    </div>
</x-layouts.app>
