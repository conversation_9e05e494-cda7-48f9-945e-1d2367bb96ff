<x-layouts.app title="Edit Property">
    <!-- Page Header -->
    <div class="mb-8">
        <flux:heading size="xl">Edit Property</flux:heading>
        <flux:subheading>Update your property listing details</flux:subheading>
    </div>

    <!-- Content -->
    <flux:card>
                    <form method="POST" action="{{ route('lister.properties.update', $property->id) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <!-- Property Type -->
                        <div>
                            <flux:select id="property_type" name="property_type" label="{{ __('Property Type') }}" class="block mt-1 w-full" required autofocus>
                                <option value="">Select Property Type</option>
                                <option value="Apartment" {{ old('property_type', $property->property_type) == 'Apartment' ? 'selected' : '' }}>Apartment</option>
                                <option value="House/Bungalow" {{ old('property_type', $property->property_type) == 'House/Bungalow' ? 'selected' : '' }}>House/Bungalow</option>
                                <option value="Land" {{ old('property_type', $property->property_type) == 'Land' ? 'selected' : '' }}>Land</option>
                                <option value="Single Room" {{ old('property_type', $property->property_type) == 'Single Room' ? 'selected' : '' }}>Single Room</option>
                            </flux:select>
                        </div>

                        <!-- Listing Type -->
                        <div class="mt-4">
                            <flux:select id="listing_type" name="listing_type" label="{{ __('Listing Type') }}" class="block mt-1 w-full" required>
                                <option value="">Select Listing Type</option>
                                <option value="For Sale" {{ old('listing_type', $property->listing_type) == 'For Sale' ? 'selected' : '' }}>For Sale</option>
                                <option value="For Rent" {{ old('listing_type', $property->listing_type) == 'For Rent' ? 'selected' : '' }}>For Rent</option>
                            </flux:select>
                        </div>

                        <!-- Title -->
                        <div class="mt-4">
                            <flux:input id="title" class="block mt-1 w-full" type="text" name="title" label="{{ __('Title') }}" :value="old('title', $property->title)" required />
                        </div>

                        <!-- Description -->
                        <div class="mt-4">
                            <flux:textarea id="description" name="description" label="{{ __('Description') }}" class="block mt-1 w-full" required>{{ old('description', $property->description) }}</flux:textarea>
                        </div>

                        <!-- Price -->
                        <div class="mt-4">
                            <flux:input id="price" class="block mt-1 w-full" type="number" step="0.01" name="price" label="{{ __('Price') }}" :value="old('price', $property->price)" required />
                        </div>

                        <!-- Currency (Optional, default USD) -->
                        <div class="mt-4">
                            <flux:input id="currency" class="block mt-1 w-full" type="text" name="currency" label="{{ __('Currency (e.g., USD, EUR)') }}" :value="old('currency', $property->currency)" />
                        </div>

                        <!-- Address Line 1 -->
                        <div class="mt-4">
                            <flux:input id="address_line_1" class="block mt-1 w-full" type="text" name="address_line_1" label="{{ __('Address Line 1') }}" :value="old('address_line_1', $property->address_line_1)" required />
                        </div>

                        <!-- City -->
                        <div class="mt-4">
                            <flux:input id="city" class="block mt-1 w-full" type="text" name="city" label="{{ __('City') }}" :value="old('city', $property->city)" required />
                        </div>

                        <!-- State/Region -->
                        <div class="mt-4">
                            <flux:input id="state_region" class="block mt-1 w-full" type="text" name="state_region" label="{{ __('State/Region') }}" :value="old('state_region', $property->state_region)" />
                        </div>

                        <!-- Zip Code -->
                        <div class="mt-4">
                            <flux:input id="zip_code" class="block mt-1 w-full" type="text" name="zip_code" label="{{ __('Zip Code') }}" :value="old('zip_code', $property->zip_code)" />
                        </div>

                        <!-- Dynamic Features (simplified for MVP) -->
                        <div class="mt-4">
                            <flux:textarea id="features" name="features" label="{{ __('Features (e.g., Bedrooms, Bathrooms, Square Footage, Plot Size)') }}" class="block mt-1 w-full" rows="5">{{ old('features', json_encode($property->features, JSON_PRETTY_PRINT)) }}</flux:textarea>
                            <p class="text-sm text-gray-600 mb-2">Add features as key-value pairs. Example: "bedrooms": "3", "bathrooms": "2", "square_footage": "1500"</p>
                        </div>

                        <!-- Existing Images -->
                        @if ($property->images)
                            <div class="mt-4">
                                <flux:label>Current Images</flux:label>
                                <div class="flex flex-wrap gap-2 mt-2">
                                    @foreach ($property->images as $image)
                                        <img src="{{ asset($image) }}" alt="Property Image" class="w-24 h-24 object-cover rounded-md">
                                    @endforeach
                                </div>
                                <flux:text size="sm" variant="muted" class="mt-2">New images will be added. To remove existing images, you will need to re-upload the desired set of images.</flux:text>
                            </div>
                        @endif

                        <!-- New Images -->
                        <div class="mt-4">
                            <flux:file name="images[]" multiple label="Add New Property Images (Max 15 total)" description="Select additional images for your property" />
                            @error('images.*')
                                <flux:error>{{ $message }}</flux:error>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div class="mt-4">
                            <flux:select id="status" name="status" label="{{ __('Status') }}" class="block mt-1 w-full">
                                <option value="draft" {{ old('status', $property->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                                <option value="published" {{ old('status', $property->status) == 'published' ? 'selected' : '' }}>Published</option>
                                <option value="sold" {{ old('status', $property->status) == 'sold' ? 'selected' : '' }}>Sold</option>
                                <option value="rented" {{ old('status', $property->status) == 'rented' ? 'selected' : '' }}>Rented</option>
                                <option value="under_offer" {{ old('status', $property->status) == 'under_offer' ? 'selected' : '' }}>Under Offer</option>
                            </flux:select>
                        </div>

                        <div class="flex items-center justify-end mt-4">
                            <flux:button type="submit" variant="primary">
                                {{ __('Update Property') }}
                            </flux:button>
                        </div>
                    </form>
    </flux:card>
</x-layouts.app>
